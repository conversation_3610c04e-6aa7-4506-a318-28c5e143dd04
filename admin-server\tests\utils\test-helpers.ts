/**
 * Test Helper Utilities
 * Common utilities and mocks for testing
 */

import { render, RenderOptions } from '@testing-library/react';
import { ReactElement } from 'react';
import { UserRole } from '@/types';

// Mock data generators
export const mockUser = (overrides = {}) => ({
  id: 'test-user-id',
  email: '<EMAIL>',
  name: 'Test User',
  role: UserRole.ADMIN,
  isActive: true,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  ...overrides,
});

export const mockPayment = (overrides = {}) => ({
  id: 'test-payment-id',
  student_id: 'STU001', // Database uses snake_case
  amount: 150.00,
  payment_type: 'tuition',
  payment_method: 'card',
  description: 'Test payment',
  status: 'completed',
  processed_by: 'test-user-id',
  start_date: new Date('2024-01-15'),
  end_date: new Date('2024-02-15'),
  debt_amount: 0,
  is_debt_payment: false,
  created_at: new Date(),
  updated_at: new Date(),
  ...overrides,
});

export const mockInvoice = (overrides = {}) => ({
  id: 'test-invoice-id',
  studentId: 'STU001',
  amount: 200.00,
  dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
  status: 'pending',
  createdBy: 'test-user-id',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  ...overrides,
});

export const mockCabinet = (overrides = {}) => ({
  id: 'test-cabinet-id',
  name: 'Test Cabinet',
  capacity: 10,
  equipment: ['Projector', 'Whiteboard'],
  hourlyRate: 25.00,
  isAvailable: true,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  ...overrides,
});

export const mockBooking = (overrides = {}) => ({
  id: 'test-booking-id',
  cabinetId: 'test-cabinet-id',
  date: new Date().toISOString().split('T')[0],
  startTime: '09:00',
  endTime: '10:00',
  bookedBy: 'test-user-id',
  purpose: 'Test meeting',
  status: 'confirmed',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  ...overrides,
});

// API response helpers
export const mockApiResponse = (data: any, success = true, message = 'Success') => ({
  data,
  success,
  message,
});

export const mockErrorResponse = (error = 'Test error', status = 500) => ({
  success: false,
  error,
  status,
});

export const mockPaginatedResponse = (data: any[], page = 1, limit = 10, total?: number) => ({
  data,
  pagination: {
    page,
    limit,
    total: total || data.length,
    totalPages: Math.ceil((total || data.length) / limit),
    hasNext: page < Math.ceil((total || data.length) / limit),
    hasPrev: page > 1,
  },
  success: true,
  message: 'Data retrieved successfully',
});

// Fetch mock helpers
export const mockFetch = (response: any, ok = true, status = 200) => {
  const mockResponse = {
    ok,
    status,
    json: jest.fn().mockResolvedValue(response),
    text: jest.fn().mockResolvedValue(JSON.stringify(response)),
  };
  
  (global.fetch as jest.Mock).mockResolvedValue(mockResponse);
  return mockResponse;
};

export const mockFetchError = (error = 'Network error') => {
  (global.fetch as jest.Mock).mockRejectedValue(new Error(error));
};

// Local storage helpers
export const mockLocalStorage = (data: Record<string, string> = {}) => {
  const defaultData = {
    token: 'test-token',
    user: JSON.stringify(mockUser()),
    ...data,
  };
  
  Object.keys(defaultData).forEach(key => {
    localStorage.setItem(key, defaultData[key as keyof typeof defaultData]);
  });
};

export const clearLocalStorage = () => {
  localStorage.clear();
};

// Form testing helpers
export const fillForm = async (container: HTMLElement, formData: Record<string, string>) => {
  const { fireEvent } = await import('@testing-library/react');
  
  Object.entries(formData).forEach(([name, value]) => {
    const input = container.querySelector(`[name="${name}"]`) as HTMLInputElement;
    if (input) {
      fireEvent.change(input, { target: { value } });
    }
  });
};

export const submitForm = async (container: HTMLElement) => {
  const { fireEvent } = await import('@testing-library/react');
  
  const form = container.querySelector('form');
  if (form) {
    fireEvent.submit(form);
  }
};

// Wait utilities
export const waitForElement = async (callback: () => HTMLElement | null, timeout = 5000) => {
  const startTime = Date.now();
  
  while (Date.now() - startTime < timeout) {
    const element = callback();
    if (element) {
      return element;
    }
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  throw new Error('Element not found within timeout');
};

export const waitForCondition = async (condition: () => boolean, timeout = 5000) => {
  const startTime = Date.now();
  
  while (Date.now() - startTime < timeout) {
    if (condition()) {
      return true;
    }
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  throw new Error('Condition not met within timeout');
};

// Custom render function with providers
export const renderWithProviders = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => {
  // Add any providers here if needed (e.g., Context providers, Router providers)
  return render(ui, options);
};

// Database mock helpers
export const mockDatabaseQuery = (result: any) => {
  const mockQuery = jest.fn().mockResolvedValue({
    rows: Array.isArray(result) ? result : [result],
    rowCount: Array.isArray(result) ? result.length : 1,
  });
  
  return mockQuery;
};

export const mockDatabaseError = (error = 'Database error') => {
  const mockQuery = jest.fn().mockRejectedValue(new Error(error));
  return mockQuery;
};

// Authentication helpers
export const mockAuthenticatedUser = (user = mockUser()) => {
  mockLocalStorage({
    token: 'valid-token',
    user: JSON.stringify(user),
  });
  
  // Mock successful auth verification
  mockFetch(mockApiResponse(user));
};

export const mockUnauthenticatedUser = () => {
  clearLocalStorage();
  mockFetch(mockErrorResponse('Not authenticated', 401), false, 401);
};

// Date helpers
export const mockDate = (date: string | Date) => {
  const mockDate = new Date(date);
  jest.spyOn(global, 'Date').mockImplementation(() => mockDate);
  return mockDate;
};

export const restoreDate = () => {
  jest.restoreAllMocks();
};

// Console helpers
export const suppressConsoleErrors = () => {
  const originalError = console.error;
  console.error = jest.fn();
  return () => {
    console.error = originalError;
  };
};

export const suppressConsoleWarnings = () => {
  const originalWarn = console.warn;
  console.warn = jest.fn();
  return () => {
    console.warn = originalWarn;
  };
};
