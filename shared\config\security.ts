/**
 * UNIFIED SECURITY CONFIGURATION
 * Centralizes security settings and policies across both servers
 */

export interface SecurityConfig {
  // Authentication settings
  auth: {
    jwtExpiresIn: string;
    refreshTokenExpiresIn: string;
    bcryptRounds: number;
    maxLoginAttempts: number;
    lockoutDurationMs: number;
    sessionTimeoutMs: number;
  };

  // Rate limiting settings
  rateLimiting: {
    login: {
      maxAttempts: number;
      windowMs: number;
    };
    api: {
      maxRequests: number;
      windowMs: number;
    };
    passwordReset: {
      maxAttempts: number;
      windowMs: number;
    };
  };

  // Password policy
  passwordPolicy: {
    minLength: number;
    requireUppercase: boolean;
    requireLowercase: boolean;
    requireNumbers: boolean;
    requireSpecialChars: boolean;
    maxAge: number; // days
    preventReuse: number; // number of previous passwords to check
  };

  // CORS settings
  cors: {
    allowedOrigins: string[];
    allowedMethods: string[];
    allowedHeaders: string[];
    credentials: boolean;
  };

  // Security headers
  headers: {
    contentSecurityPolicy: string;
    xFrameOptions: string;
    xContentTypeOptions: string;
    referrerPolicy: string;
    strictTransportSecurity: string;
  };

  // File upload security
  fileUpload: {
    maxFileSize: number; // bytes
    allowedMimeTypes: string[];
    allowedExtensions: string[];
    scanForMalware: boolean;
  };

  // API security
  api: {
    requireApiKey: boolean;
    enableRequestLogging: boolean;
    enableResponseSanitization: boolean;
    maxRequestSize: number; // bytes
  };
}

/**
 * Default security configuration
 */
const defaultConfig: SecurityConfig = {
  auth: {
    jwtExpiresIn: process.env.JWT_EXPIRES_IN || '24h',
    refreshTokenExpiresIn: process.env.REFRESH_TOKEN_EXPIRES_IN || '7d',
    bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS || '12'),
    maxLoginAttempts: parseInt(process.env.MAX_LOGIN_ATTEMPTS || '5'),
    lockoutDurationMs: parseInt(process.env.LOCKOUT_DURATION_MS || '1800000'), // 30 minutes
    sessionTimeoutMs: parseInt(process.env.SESSION_TIMEOUT_MS || '86400000'), // 24 hours
  },

  rateLimiting: {
    login: {
      maxAttempts: parseInt(process.env.LOGIN_RATE_LIMIT_ATTEMPTS || '5'),
      windowMs: parseInt(process.env.LOGIN_RATE_LIMIT_WINDOW || '900000'), // 15 minutes
    },
    api: {
      maxRequests: parseInt(process.env.API_RATE_LIMIT_REQUESTS || '100'),
      windowMs: parseInt(process.env.API_RATE_LIMIT_WINDOW || '60000'), // 1 minute
    },
    passwordReset: {
      maxAttempts: parseInt(process.env.PASSWORD_RESET_RATE_LIMIT_ATTEMPTS || '3'),
      windowMs: parseInt(process.env.PASSWORD_RESET_RATE_LIMIT_WINDOW || '3600000'), // 1 hour
    },
  },

  passwordPolicy: {
    minLength: parseInt(process.env.PASSWORD_MIN_LENGTH || '8'),
    requireUppercase: process.env.PASSWORD_REQUIRE_UPPERCASE !== 'false',
    requireLowercase: process.env.PASSWORD_REQUIRE_LOWERCASE !== 'false',
    requireNumbers: process.env.PASSWORD_REQUIRE_NUMBERS !== 'false',
    requireSpecialChars: process.env.PASSWORD_REQUIRE_SPECIAL !== 'false',
    maxAge: parseInt(process.env.PASSWORD_MAX_AGE_DAYS || '90'),
    preventReuse: parseInt(process.env.PASSWORD_PREVENT_REUSE || '5'),
  },

  cors: {
    allowedOrigins: process.env.CORS_ALLOWED_ORIGINS?.split(',') || [
      'http://localhost:3000',
      'http://localhost:3001',
      'https://admin.innovativecentre.com',
      'https://staff.innovativecentre.com'
    ],
    allowedMethods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: [
      'Content-Type',
      'Authorization',
      'X-Requested-With',
      'Accept',
      'Origin',
      'Cache-Control',
      'X-File-Name'
    ],
    credentials: true,
  },

  headers: {
    contentSecurityPolicy: process.env.CSP_POLICY || 
      "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:; frame-ancestors 'none';",
    xFrameOptions: 'DENY',
    xContentTypeOptions: 'nosniff',
    referrerPolicy: 'strict-origin-when-cross-origin',
    strictTransportSecurity: 'max-age=31536000; includeSubDomains; preload',
  },

  fileUpload: {
    maxFileSize: parseInt(process.env.MAX_FILE_SIZE || '10485760'), // 10MB
    allowedMimeTypes: [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'application/pdf',
      'text/csv',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ],
    allowedExtensions: ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.pdf', '.csv', '.xls', '.xlsx'],
    scanForMalware: process.env.ENABLE_MALWARE_SCANNING === 'true',
  },

  api: {
    requireApiKey: process.env.REQUIRE_API_KEY === 'true',
    enableRequestLogging: process.env.ENABLE_REQUEST_LOGGING !== 'false',
    enableResponseSanitization: process.env.ENABLE_RESPONSE_SANITIZATION !== 'false',
    maxRequestSize: parseInt(process.env.MAX_REQUEST_SIZE || '1048576'), // 1MB
  },
};

/**
 * Get security configuration with environment overrides
 */
export function getSecurityConfig(): SecurityConfig {
  return defaultConfig;
}

/**
 * Validate password against security policy
 */
export function validatePassword(password: string, policy?: SecurityConfig['passwordPolicy']): {
  isValid: boolean;
  errors: string[];
} {
  const config = policy || getSecurityConfig().passwordPolicy;
  const errors: string[] = [];

  if (password.length < config.minLength) {
    errors.push(`Password must be at least ${config.minLength} characters long`);
  }

  if (config.requireUppercase && !/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }

  if (config.requireLowercase && !/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }

  if (config.requireNumbers && !/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  }

  if (config.requireSpecialChars && !/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    errors.push('Password must contain at least one special character');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Check if file upload is allowed
 */
export function validateFileUpload(file: {
  name: string;
  size: number;
  type: string;
}): {
  isValid: boolean;
  errors: string[];
} {
  const config = getSecurityConfig().fileUpload;
  const errors: string[] = [];

  // Check file size
  if (file.size > config.maxFileSize) {
    errors.push(`File size exceeds maximum allowed size of ${config.maxFileSize} bytes`);
  }

  // Check MIME type
  if (!config.allowedMimeTypes.includes(file.type)) {
    errors.push(`File type ${file.type} is not allowed`);
  }

  // Check file extension
  const extension = '.' + file.name.split('.').pop()?.toLowerCase();
  if (!config.allowedExtensions.includes(extension)) {
    errors.push(`File extension ${extension} is not allowed`);
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Sanitize user input to prevent XSS
 */
export function sanitizeInput(input: string): string {
  return input
    .replace(/[<>]/g, '') // Remove < and >
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .trim();
}

/**
 * Generate secure random string
 */
export function generateSecureToken(length: number = 32): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * Check if IP address is in allowed range
 */
export function isIPAllowed(ip: string, allowedRanges: string[] = []): boolean {
  if (allowedRanges.length === 0) {
    return true; // No restrictions
  }

  // Simple IP range checking (can be enhanced with proper CIDR support)
  return allowedRanges.some(range => {
    if (range === ip) return true;
    if (range.includes('*')) {
      const pattern = range.replace(/\*/g, '.*');
      return new RegExp(`^${pattern}$`).test(ip);
    }
    return false;
  });
}

// Export the default configuration
export default getSecurityConfig();
